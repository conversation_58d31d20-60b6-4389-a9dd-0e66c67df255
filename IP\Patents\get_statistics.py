import os
import sys
sys.path.append(os.getcwd())
import psycopg2
import psycopg2.extras
import logging
import time
from dotenv import load_dotenv
from IP.Patents.patent_db_grant import get_db_connection # Assuming this is the correct path to get_db_connection
from qdrant_client import QdrantClient
from qdrant_client.models import FieldCondition, MatchValue, Filter

# Load environment variables
load_dotenv()

previous_utility_patent_reg_no = None

# Configure logging
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'get_statistics.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def calculate_percentage(count, total):
    """Calculates percentage, handling division by zero."""
    return (count / total * 100) if total > 0 else 0

def get_total_patents_by_type(conn, mode):
    """
    Gets the total number of patents and their distribution by patent_type.
    """
    logger.info(f"Fetching total patents by patent_type for mode: {mode}...")
    table_name = "patents"
    if mode == "all":
        table_name += "_all"

    query = f"""
    SELECT patent_type, COUNT(*) AS count
    FROM {table_name}
    GROUP BY patent_type
    ORDER BY patent_type;
    """
    total_query = f"SELECT COUNT(*) FROM {table_name};"
    
    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute(total_query)
        total_patents = cursor.fetchone()[0]

        cursor.execute(query)
        results = cursor.fetchall()
    
    stats = {
        "total_patents": total_patents,
        "by_patent_type": []
    }
    for row in results:
        stats["by_patent_type"].append({
            "patent_type": row['patent_type'],
            "count": row['count'],
            "percentage": calculate_percentage(row['count'], total_patents)
        })
    logger.info("Finished fetching total patents by patent_type.")
    return stats

def get_total_patents_by_aggregated_type(conn, mode):
    """
    Gets the total number of patents and their distribution by aggregated patent_type
    (first character of patent_type).
    """
    logger.info(f"Fetching total patents by aggregated patent_type for mode: {mode}...")
    table_name = "patents"
    if mode == "all":
        table_name += "_all"

    query = f"""
    SELECT SUBSTRING(patent_type, 1, 1) AS aggregated_patent_type, COUNT(*) AS count
    FROM {table_name}
    GROUP BY aggregated_patent_type
    ORDER BY aggregated_patent_type;
    """
    total_query = f"SELECT COUNT(*) FROM {table_name};"

    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute(total_query)
        total_patents = cursor.fetchone()[0]

        cursor.execute(query)
        results = cursor.fetchall()
    
    stats = {
        "total_patents": total_patents,
        "by_aggregated_patent_type": []
    }
    for row in results:
        stats["by_aggregated_patent_type"].append({
            "aggregated_patent_type": row['aggregated_patent_type'],
            "count": row['count'],
            "percentage": calculate_percentage(row['count'], total_patents)
        })
    logger.info("Finished fetching total patents by aggregated patent_type.")
    return stats

def get_patents_without_cpc_ipc(conn, mode):
    """
    Gets the number of patents without a cpc_ipc assignment, split by patent_type
    and aggregated patent_type.
    """
    logger.info(f"Fetching patents without CPC/IPC assignment for mode: {mode}...")
    patent_table_name = "patents"
    cpc_assignment_table_name = "patents_cpc_ipc_assignments"
    if mode == "all":
        patent_table_name += "_all"
        cpc_assignment_table_name += "_all"

    query = f"""
    SELECT pr.patent_type,
           SUBSTRING(pr.patent_type, 1, 1) AS aggregated_patent_type,
           COUNT(pr.id) AS count
    FROM {patent_table_name} pr
    LEFT JOIN {cpc_assignment_table_name} pcpa ON pr.id = pcpa.patents_id
    WHERE pcpa.patents_id IS NULL
    GROUP BY pr.patent_type, SUBSTRING(pr.patent_type, 1, 1)
    ORDER BY pr.patent_type;
    """
    total_query = f"SELECT COUNT(*) FROM {patent_table_name};"

    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute(total_query)
        total_patents = cursor.fetchone()[0]

        cursor.execute(query)
        results = cursor.fetchall()
    
    stats = {
        "total_patents": total_patents,
        "without_cpc_ipc": {
            "total_missing": 0,
            "by_patent_type": [],
            "by_aggregated_patent_type": {}
        }
    }
    
    total_missing_cpc_ipc = 0
    for row in results:
        total_missing_cpc_ipc += row['count']
        stats["without_cpc_ipc"]["by_patent_type"].append({
            "patent_type": row['patent_type'],
            "count": row['count'],
            "percentage": calculate_percentage(row['count'], total_patents)
        })
        
        agg_type = row['aggregated_patent_type']
        if agg_type not in stats["without_cpc_ipc"]["by_aggregated_patent_type"]:
            stats["without_cpc_ipc"]["by_aggregated_patent_type"][agg_type] = 0
        stats["without_cpc_ipc"]["by_aggregated_patent_type"][agg_type] += row['count']
    
    stats["without_cpc_ipc"]["total_missing"] = total_missing_cpc_ipc
    stats["without_cpc_ipc"]["total_percentage"] = calculate_percentage(total_missing_cpc_ipc, total_patents)

    # Convert aggregated_patent_type counts to list of dicts with percentages
    agg_list = []
    for agg_type, count in stats["without_cpc_ipc"]["by_aggregated_patent_type"].items():
        agg_list.append({
            "aggregated_patent_type": agg_type,
            "count": count,
            "percentage": calculate_percentage(count, total_patents)
        })
    stats["without_cpc_ipc"]["by_aggregated_patent_type"] = sorted(agg_list, key=lambda x: x['aggregated_patent_type'])

    logger.info("Finished fetching patents without CPC/IPC assignment.")
    return stats

def get_patents_without_uspc(conn, mode):
    """
    Gets the number of patents without a uspc assignment, split by patent_type
    and aggregated patent_type.
    """
    logger.info(f"Fetching patents without USPC assignment for mode: {mode}...")
    patent_table_name = "patents"
    uspc_assignment_table_name = "patents_uspc_assignments"
    if mode == "all":
        patent_table_name += "_all"
        uspc_assignment_table_name += "_all"

    query = f"""
    SELECT pr.patent_type,
           SUBSTRING(pr.patent_type, 1, 1) AS aggregated_patent_type,
           COUNT(pr.id) AS count
    FROM {patent_table_name} pr
    LEFT JOIN {uspc_assignment_table_name} pua ON pr.id = pua.patents_id
    WHERE pua.patents_id IS NULL
    GROUP BY pr.patent_type, SUBSTRING(pr.patent_type, 1, 1)
    ORDER BY pr.patent_type;
    """
    total_query = f"SELECT COUNT(*) FROM {patent_table_name};"

    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute(total_query)
        total_patents = cursor.fetchone()[0]

        cursor.execute(query)
        results = cursor.fetchall()
    
    stats = {
        "total_patents": total_patents,
        "without_uspc": {
            "total_missing": 0,
            "by_patent_type": [],
            "by_aggregated_patent_type": {}
        }
    }
    
    total_missing_uspc = 0
    for row in results:
        total_missing_uspc += row['count']
        stats["without_uspc"]["by_patent_type"].append({
            "patent_type": row['patent_type'],
            "count": row['count'],
            "percentage": calculate_percentage(row['count'], total_patents)
        })
        
        agg_type = row['aggregated_patent_type']
        if agg_type not in stats["without_uspc"]["by_aggregated_patent_type"]:
            stats["without_uspc"]["by_aggregated_patent_type"][agg_type] = 0
        stats["without_uspc"]["by_aggregated_patent_type"][agg_type] += row['count']
    
    stats["without_uspc"]["total_missing"] = total_missing_uspc
    stats["without_uspc"]["total_percentage"] = calculate_percentage(total_missing_uspc, total_patents)

    # Convert aggregated_patent_type counts to list of dicts with percentages
    agg_list = []
    for agg_type, count in stats["without_uspc"]["by_aggregated_patent_type"].items():
        agg_list.append({
            "aggregated_patent_type": agg_type,
            "count": count,
            "percentage": calculate_percentage(count, total_patents)
        })
    stats["without_uspc"]["by_aggregated_patent_type"] = sorted(agg_list, key=lambda x: x['aggregated_patent_type'])

    logger.info("Finished fetching patents without USPC assignment.")
    return stats

def get_patents_without_loc_code(conn, mode):
    """
    Gets the number of patents without a loc_code, split by patent_type
    and aggregated patent_type.
    """
    logger.info(f"Fetching patents without Locarno code for mode: {mode}...")
    table_name = "patents"
    if mode == "all":
        table_name += "_all"

    query = f"""
    SELECT patent_type,
           SUBSTRING(patent_type, 1, 1) AS aggregated_patent_type,
           COUNT(*) AS count
    FROM {table_name}
    WHERE loc_code IS NULL OR loc_code = ''
    GROUP BY patent_type, SUBSTRING(patent_type, 1, 1)
    ORDER BY patent_type;
    """
    total_query = f"SELECT COUNT(*) FROM {table_name};"

    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute(total_query)
        total_patents = cursor.fetchone()[0]

        cursor.execute(query)
        results = cursor.fetchall()
    
    stats = {
        "total_patents": total_patents,
        "without_loc_code": {
            "total_missing": 0,
            "by_patent_type": [],
            "by_aggregated_patent_type": {}
        }
    }
    
    total_missing_loc_code = 0
    for row in results:
        total_missing_loc_code += row['count']
        stats["without_loc_code"]["by_patent_type"].append({
            "patent_type": row['patent_type'],
            "count": row['count'],
            "percentage": calculate_percentage(row['count'], total_patents)
        })
        
        agg_type = row['aggregated_patent_type']
        if agg_type not in stats["without_loc_code"]["by_aggregated_patent_type"]:
            stats["without_loc_code"]["by_aggregated_patent_type"][agg_type] = 0
        stats["without_loc_code"]["by_aggregated_patent_type"][agg_type] += row['count']
    
    stats["without_loc_code"]["total_missing"] = total_missing_loc_code
    stats["without_loc_code"]["total_percentage"] = calculate_percentage(total_missing_loc_code, total_patents)

    # Convert aggregated_patent_type counts to list of dicts with percentages
    agg_list = []
    for agg_type, count in stats["without_loc_code"]["by_aggregated_patent_type"].items():
        agg_list.append({
            "aggregated_patent_type": agg_type,
            "count": count,
            "percentage": calculate_percentage(count, total_patents)
        })
    stats["without_loc_code"]["by_aggregated_patent_type"] = sorted(agg_list, key=lambda x: x['aggregated_patent_type'])

    logger.info("Finished fetching patents without Locarno code.")
    return stats

def get_patents_without_title(conn, mode):
    """
    Gets the number of patents without a patent_title.
    """
    logger.info(f"Fetching patents without patent title for mode: {mode}...")
    table_name = "patents"
    if mode == "all":
        table_name += "_all"

    query = f"""
    SELECT patent_type,
           SUBSTRING(patent_type, 1, 1) AS aggregated_patent_type,
           COUNT(*) AS count
    FROM {table_name}
    WHERE patent_title IS NULL OR patent_title = ''
    GROUP BY patent_type, SUBSTRING(patent_type, 1, 1)
    ORDER BY patent_type;
    """
    total_query = f"SELECT COUNT(*) FROM {table_name};"

    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute(total_query)
        total_patents = cursor.fetchone()[0]

        cursor.execute(query)
        results = cursor.fetchall()
    
    stats = {
        "total_patents": total_patents,
        "without_patent_title": {
            "total_missing": 0,
            "by_patent_type": [],
            "by_aggregated_patent_type": {}
        }
    }
    
    total_missing_title = 0
    for row in results:
        total_missing_title += row['count']
        stats["without_patent_title"]["by_patent_type"].append({
            "patent_type": row['patent_type'],
            "count": row['count'],
            "percentage": calculate_percentage(row['count'], total_patents)
        })
        
        agg_type = row['aggregated_patent_type']
        if agg_type not in stats["without_patent_title"]["by_aggregated_patent_type"]:
            stats["without_patent_title"]["by_aggregated_patent_type"][agg_type] = 0
        stats["without_patent_title"]["by_aggregated_patent_type"][agg_type] += row['count']
    
    stats["without_patent_title"]["total_missing"] = total_missing_title
    stats["without_patent_title"]["total_percentage"] = calculate_percentage(total_missing_title, total_patents)

    # Convert aggregated_patent_type counts to list of dicts with percentages
    agg_list = []
    for agg_type, count in stats["without_patent_title"]["by_aggregated_patent_type"].items():
        agg_list.append({
            "aggregated_patent_type": agg_type,
            "count": count,
            "percentage": calculate_percentage(count, total_patents)
        })
    stats["without_patent_title"]["by_aggregated_patent_type"] = sorted(agg_list, key=lambda x: x['aggregated_patent_type'])

    logger.info("Finished fetching patents without patent title.")
    return stats
def get_example_missing_cpc_b_type_patents(conn, mode, limit=10):
    """
    Gets example document_ids for patents with aggregated_patent_type 'B'
    that are missing CPC classification.
    """
    logger.info(f"Fetching {limit} example patents with aggregated_patent_type 'B' missing CPC/IPC assignment for mode: {mode}...")
    patent_table_name = "patents"
    cpc_assignment_table_name = "patents_cpc_ipc_assignments"
    if mode == "all":
        patent_table_name += "_all"
        cpc_assignment_table_name += "_all"

    query = f"""
    SELECT pr.document_id
    FROM {patent_table_name} pr
    LEFT JOIN {cpc_assignment_table_name} pcpa ON pr.id = pcpa.patents_id
    WHERE SUBSTRING(pr.patent_type, 1, 1) = 'B'
    AND pcpa.patents_id IS NULL
    LIMIT %s;
    """
    
    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute(query, (limit,))
        results = cursor.fetchall()
    
    document_ids = [row['document_id'] for row in results]
    logger.info(f"Finished fetching {len(document_ids)} example patents.")
    return document_ids

def get_s_type_patents_in_all_not_in_subset(conn, limit=10):
    """
    Gets the number of patents with Aggregated Type 'S' that are in 'patents_all'
    but not in 'patents', and provides example document_ids.
    """
    logger.info(f"Fetching patents with Aggregated Type 'S' in patents_all but not in patents (using reg_no to match), and {limit} examples...")
    
    count_query = """
    SELECT COUNT(pa.reg_no)
    FROM patents_all pa
    LEFT JOIN patents p ON pa.reg_no = p.reg_no
    WHERE SUBSTRING(pa.patent_type, 1, 1) = 'S'
    AND p.reg_no IS NULL;
    """

    examples_query = """
    SELECT pa.document_id
    FROM patents_all pa
    LEFT JOIN patents p ON pa.reg_no = p.reg_no
    WHERE SUBSTRING(pa.patent_type, 1, 1) = 'S'
    AND p.reg_no IS NULL
    LIMIT %s;
    """
    
    total_count = 0
    document_ids = []

    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute(count_query)
        total_count = cursor.fetchone()[0]

        cursor.execute(examples_query, (limit,))
        results = cursor.fetchall()
        document_ids = [row['document_id'] for row in results]
    
    logger.info(f"Finished fetching patents with Aggregated Type 'S' in patents_all but not in patents. Count: {total_count}, Examples: {len(document_ids)}.")
    return {"count": total_count, "examples": document_ids}


def print_statistics(stats):
    """Prints the gathered statistics in a readable format."""
    total_patents = stats.get("total_patents", 0)
    print("\n--- Patent Database Statistics ---")
    print(f"Total number of patents in the database: {total_patents}\n")

    # Total number of patent (and %) by patent_type
    print("1. Total number of patents by Patent Type:")
    for item in stats.get("by_patent_type", []):
        print(f"   - {item['patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("-" * 40)

    # Total number of patent (and %) by aggregated patent_type
    print("\n2. Total number of patents by Aggregated Patent Type (first character):")
    for item in stats.get("by_aggregated_patent_type", []):
        print(f"   - {item['aggregated_patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("-" * 40)

    # Number (and %) of patent without a cpc_ipc assignement
    cpc_ipc_stats = stats.get("without_cpc_ipc", {})
    print(f"\n3. Patents without CPC/IPC Assignment: {cpc_ipc_stats.get('total_missing', 0)} ({cpc_ipc_stats.get('total_percentage', 0):.2f}%)")
    print("   By Patent Type:")
    for item in cpc_ipc_stats.get("by_patent_type", []):
        print(f"     - {item['patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("   By Aggregated Patent Type:")
    for item in cpc_ipc_stats.get("by_aggregated_patent_type", []):
        print(f"     - {item['aggregated_patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("-" * 40)

    # Number (and %) of patent without a uspc assignement
    uspc_stats = stats.get("without_uspc", {})
    print(f"\n4. Patents without USPC Assignment: {uspc_stats.get('total_missing', 0)} ({uspc_stats.get('total_percentage', 0):.2f}%)")
    print("   By Patent Type:")
    for item in uspc_stats.get("by_patent_type", []):
        print(f"     - {item['patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("   By Aggregated Patent Type:")
    for item in uspc_stats.get("by_aggregated_patent_type", []):
        print(f"     - {item['aggregated_patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("-" * 40)

    # Number (and %) of patent without a loc_code
    loc_code_stats = stats.get("without_loc_code", {})
    print(f"\n5. Patents without Locarno Code: {loc_code_stats.get('total_missing', 0)} ({loc_code_stats.get('total_percentage', 0):.2f}%)")
    print("   By Patent Type:")
    for item in loc_code_stats.get("by_patent_type", []):
        print(f"     - {item['patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("   By Aggregated Patent Type:")
    for item in loc_code_stats.get("by_aggregated_patent_type", []):
        print(f"     - {item['aggregated_patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("-" * 40)

    # Number (and %) of patent without a patent_title
    title_stats = stats.get("without_patent_title", {})
    print(f"\n6. Patents without Patent Title: {title_stats.get('total_missing', 0)} ({title_stats.get('total_percentage', 0):.2f}%)")
    print("   By Patent Type:")
    for item in title_stats.get("by_patent_type", []):
        print(f"     - {item['patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("   By Aggregated Patent Type:")
    for item in title_stats.get("by_aggregated_patent_type", []):
        print(f"     - {item['aggregated_patent_type']}: {item['count']} ({item['percentage']:.2f}%)")
    print("-" * 40)

    # Example patents with aggregated_patent_type 'B' missing CPC/IPC assignment
    example_b_missing_cpc = stats.get("example_b_missing_cpc", [])
    if example_b_missing_cpc:
        print("\n7. 10 Example Patents (Aggregated Type 'B') Missing CPC/IPC Assignment:")
        for doc_id in example_b_missing_cpc:
            print(f"   - {doc_id}")
        print("-" * 40)

    # Number of patents with Aggregated Type 'S' in patents_all but not in patents
    s_type_in_all_not_in_subset_stats = stats.get("s_type_in_all_not_in_subset", {})
    if s_type_in_all_not_in_subset_stats.get("count", 0) > 0:
        print(f"\n8. Patents with Aggregated Type 'S' in 'patents_all' but not in 'patents': {s_type_in_all_not_in_subset_stats['count']}")
        if s_type_in_all_not_in_subset_stats.get("examples"):
            print("   10 Example Document IDs:")
            for doc_id in s_type_in_all_not_in_subset_stats["examples"]:
                print(f"     - {doc_id}")
        print("-" * 40)


def database_stats(mode="subset"): # Default to "subset" if not provided
    conn = None
    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Could not establish database connection. Exiting.")
            return

        all_stats = {}
        
        # Get total patents by type
        stats_by_type = get_total_patents_by_type(conn, mode)
        all_stats.update(stats_by_type)

        # Get total patents by aggregated type
        stats_by_agg_type = get_total_patents_by_aggregated_type(conn, mode)
        all_stats.update(stats_by_agg_type)

        # Get patents without CPC/IPC assignment
        stats_no_cpc_ipc = get_patents_without_cpc_ipc(conn, mode)
        all_stats.update(stats_no_cpc_ipc)

        # Get patents without USPC assignment
        stats_no_uspc = get_patents_without_uspc(conn, mode)
        all_stats.update(stats_no_uspc)

        # Get patents without Locarno code
        stats_no_loc_code = get_patents_without_loc_code(conn, mode)
        all_stats.update(stats_no_loc_code)

        # Get patents without patent title
        stats_no_title = get_patents_without_title(conn, mode)
        all_stats.update(stats_no_title)

        # Get example patents with aggregated_patent_type 'B' missing CPC/IPC assignment
        example_b_missing_cpc = get_example_missing_cpc_b_type_patents(conn, mode, limit=10)
        all_stats["example_b_missing_cpc"] = example_b_missing_cpc

        # Get patents with Aggregated Type 'S' in patents_all but not in patents
        s_type_in_all_not_in_subset = get_s_type_patents_in_all_not_in_subset(conn, limit=10)
        all_stats["s_type_in_all_not_in_subset"] = s_type_in_all_not_in_subset

        print_statistics(all_stats)

    except Exception as e:
        logger.error(f"An error occurred during statistics generation: {e}", exc_info=True)
    finally:
        if conn:
            conn.close()
            logger.info("Database connection closed.")

def qdrant_stats():
    global previous_utility_patent_reg_no
    logger.info("Fetching Qdrant statistics for patents...")
    if previous_utility_patent_reg_no is None:
        previous_utility_patent_reg_no = set()
    qdrant_url = os.environ.get("QDRANT_URL")
    qdrant_api_key = os.environ.get("QDRANT_API_KEY")

    if not qdrant_url or not qdrant_api_key:
        logger.error("QDRANT_URL or QDRANT_API_KEY not set in environment variables. Cannot connect to Qdrant.")
        return

    client = QdrantClient(url=qdrant_url, api_key=qdrant_api_key)
    collection_name = "IP_Assets"

    try:
        # 1. Total points where ip_type == Patent
        patent_filter = Filter(
            must=[
                FieldCondition(
                    key="ip_type",
                    match=MatchValue(value="Patent")
                )
            ]
        )
        
        count_result = client.count(
            collection_name=collection_name,
            count_filter=patent_filter,
            exact=True
        )
        total_patent_points = count_result.count
        
        # Retrieve all patent points to process payloads
        offset = None
        all_patent_points = []

        while True:
            pts, next_offset = client.scroll(
                collection_name=collection_name,
                scroll_filter=patent_filter,
                limit=10000,
                offset=offset,
                with_payload=True,
                with_vectors=False
            )
            all_patent_points.extend(pts)
            if next_offset is None:
                break
            offset = next_offset

        plaintiff_id_count = 0
        reg_no_count = 0
        unique_reg_no = set()
        design_patent_count = 0
        points_without_payload_count = 0
        unique_design_patent_reg_no = set()

        for point in all_patent_points:
            payload = point.payload
            if payload:
                if "plaintiff_id" in payload and payload["plaintiff_id"] is not None:
                    plaintiff_id_count += 1
                
                if "reg_no" in payload and payload["reg_no"] is not None:
                    reg_no_count += 1
                    unique_reg_no.add(payload["reg_no"])
                    
                    if isinstance(payload["reg_no"], str) and payload["reg_no"].startswith("D"):
                        design_patent_count += 1
                        unique_design_patent_reg_no.add(payload["reg_no"])
            else:
                points_without_payload_count += 1

        utility_patent_count = reg_no_count - design_patent_count
        current_utility_patent_reg_no = unique_reg_no - unique_design_patent_reg_no

        new_utility_patents = current_utility_patent_reg_no - previous_utility_patent_reg_no
        
        print("\n--- Qdrant Patent Statistics (IP_Assets Collection) ---")
        print(f"Total points with 'ip_type' == 'Patent': {total_patent_points}")
        print(f"Points with 'plaintiff_id' payload: {plaintiff_id_count}")
        print(f"Points with 'reg_no' payload: {reg_no_count}")
        print(f"Unique 'reg_no' values: {len(unique_reg_no)}")
        print(f"Design patents ('reg_no' starting with 'D'): {design_patent_count}")
        print(f"Unique design patent 'reg_no' values: {len(unique_design_patent_reg_no)}")
        print(f"Utility patents ('reg_no' not starting with 'D'): {utility_patent_count}")
        print(f"Unique utility patent 'reg_no' values: {len(current_utility_patent_reg_no)}")
        print(f"Points without payload: {points_without_payload_count}")

        if new_utility_patents:
            print("\nNew Utility Patent 'reg_no' values found:")
            for reg_no in new_utility_patents:
                print(f"   - {reg_no}")
        else:
            print("\nNo new Utility Patent 'reg_no' values found.")
        
        print("-" * 40)

        previous_utility_patent_reg_no = current_utility_patent_reg_no

    except Exception as e:
        logger.error(f"An error occurred during Qdrant statistics generation: {e}", exc_info=True)
    finally:
        # QdrantClient does not require explicit close, but good practice to log completion
        logger.info("Finished fetching Qdrant statistics.")
    

if __name__ == "__main__":
    # Initial run
    qdrant_stats()

    # Schedule qdrant_stats to run every 10 minutes
    while True:
        time.sleep(600) # Sleep for 10 minutes (600 seconds)
        qdrant_stats()