import os
import sys
sys.path.append(os.getcwd())
import logging
import psycopg2
from qdrant_client import QdrantClient
from qdrant_client.models import Filter, FieldCondition, MatchValue
from Common.uuid_utils import generate_uuid
from IP.Patents.patent_db_grant import get_db_connection
from IP.Patents.patent_parser import get_tro_cpc_ipc_classifications

# --- Logging Setup ---
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'delete_qdrant_entries.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)

def get_qdrant_patent_reg_nos(qdrant_client: QdrantClient, collection_name: str) -> tuple[dict, int]:
    """
    Retrieves all patent reg_no and their corresponding point_ids from Qdrant.
    Returns a dictionary mapping reg_no to point_id.
    """
    logging.info("Fetching patent reg_nos and point_ids from Qdrant...")
    reg_no_to_point_id = {}
    offset = None
    limit = 1000 # Fetch in batches

    patent_filter = Filter(
        must=[
            FieldCondition(key="ip_type", match=MatchValue(value="Patent"))
        ]
    )
    total_patent_points = 0
    try:
        count_result = qdrant_client.count(
            collection_name=collection_name,
            count_filter=patent_filter,
            exact=True
        )
        total_patent_points = count_result.count
        logging.info(f"Total patent entries in Qdrant (ip_type=Patent): {total_patent_points}")
    except Exception as e:
        logging.error(f"Error getting total Qdrant patent count: {e}")

    while True:
        try:
            scroll_result = qdrant_client.scroll(
                collection_name=collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="ip_type",
                            match=MatchValue(value="Patent")
                        )
                    ]
                ),
                limit=limit,
                offset=offset,
                with_payload=True,
                with_vectors=False
            )

            points = scroll_result[0] # scroll_result is a tuple (list_of_points, next_page_offset)
            next_page_offset = scroll_result[1]

            if not points:
                break

            for point in points:
                if point.payload and 'reg_no' in point.payload:
                    # For patents, point.id is generate_uuid(image_path_stem) where image_path_stem is reg_no
                    # So, point.id is generate_uuid(reg_no)
                    reg_no_to_point_id[point.payload['reg_no']] = point.id
            
            if next_page_offset is None:
                break
            offset = next_page_offset

        except Exception as e:
            logging.error(f"Error fetching Qdrant patent reg_nos: {e}")
            break
    
    logging.info(f"Found {len(reg_no_to_point_id)} patent entries with 'reg_no' in Qdrant.")
    return reg_no_to_point_id, total_patent_points

def get_db_patent_classifications(reg_nos: set) -> dict:
    """
    Retrieves CPC/IPC classifications for a given set of patent registration numbers from the database.
    Returns a dictionary mapping reg_no to a list of classification dictionaries.
    """
    logging.info(f"Fetching classifications for {len(reg_nos)} patents from PostgreSQL...")
    patent_classifications = {}
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logging.error("Failed to get database connection.")
            return {}
        cursor = conn.cursor()

        reg_nos_list = list(reg_nos)
        if not reg_nos_list:
            return {}

        # Query to join patents_all and patents_cpc_icp_assignements_all
        # and retrieve classification details.
        # Assuming 'patent_id' in patents_cpc_icp_assignements_all links to 'reg_no' in patents_all.
        sql_query = """
            SELECT
                pa.reg_no,
                pc.section,
                pc.class,
                pc.subclass,
                pc.main_group,
                pc.subgroup
            FROM
                patents_all pa
            JOIN
                patents_cpc_icp_assignements_all pc ON pa.reg_no = pc.patent_id
            WHERE
                pa.reg_no IN %s;
        """
        
        # Execute in batches for large sets of reg_nos
        batch_size = 10000 

        for i in range(0, len(reg_nos_list), batch_size):
            current_batch_reg_nos = tuple(reg_nos_list[i:i + batch_size])
            cursor.execute(sql_query, (current_batch_reg_nos,))
            for row in cursor.fetchall():
                reg_no = row[0]
                classification_data = {
                    'section': row[1],
                    'class': row[2],
                    'subclass': row[3],
                    'main_group': row[4],
                    'subgroup': row[5]
                }
                if reg_no not in patent_classifications:
                    patent_classifications[reg_no] = []
                patent_classifications[reg_no].append(classification_data)

    except psycopg2.DatabaseError as db_err:
        logging.error(f"Database error fetching patent classifications: {db_err}")
    except Exception as e:
        logging.error(f"An unexpected error occurred fetching patent classifications: {e}", exc_info=True)
    finally:
        if conn:
            conn.close()
    
    logging.info(f"Retrieved classifications for {len(patent_classifications)} patents from database.")
    return patent_classifications

def identify_patents_for_deletion(
    qdrant_reg_nos: dict,
    db_patent_classifications: dict,
    current_tro_cpc_ipc_classifications: set
) -> list:
    """
    Identifies point_ids of patents to be deleted from Qdrant.
    A patent is marked for deletion if its classification (from DB)
    matches the NEW TRO CPC/IPC classification logic.
    This implies they need to be re-indexed.
    """
    logging.info("Identifying patents for deletion...")
    point_ids_to_delete = []
    
    for reg_no, qdrant_point_id in qdrant_reg_nos.items():
        db_classifications = db_patent_classifications.get(reg_no)
        if not db_classifications:
            continue

        should_delete = False
        for entry in db_classifications:
            # Construct the NEW classification string as used in patent_parser.py line 258
            # This is the format that _tro_cpc_ipc_classifications currently holds.
            # Ensure consistency with how main_group is stripped and class is zero-filled in patent_parser.
            section = entry['section']
            cpc_class = entry['class'].zfill(2)
            subclass = entry['subclass']
            main_group = entry['main_group'].lstrip('0') # Consistent with patent_parser.py

            new_classification_string = f"{section}{cpc_class}{subclass}{main_group}"

            if new_classification_string in current_tro_cpc_ipc_classifications:
                # This patent's classification matches one of the *new* TRO CPC classifications.
                # If it's already in Qdrant, it was likely indexed with the old logic,
                # so we mark it for deletion to allow re-indexing with the new logic.
                should_delete = True
                break # No need to check further classifications for this patent

        if should_delete:
            point_ids_to_delete.append(qdrant_point_id)
            
    logging.info(f"Identified {len(point_ids_to_delete)} Qdrant entries for deletion.")
    return point_ids_to_delete

def delete_qdrant_entries(qdrant_client: QdrantClient, collection_name: str, point_ids: list):
    """
    Deletes specified point_ids from the Qdrant collection.
    """
    if not point_ids:
        logging.info("No Qdrant entries to delete.")
        return

    logging.info(f"Deleting {len(point_ids)} entries from Qdrant collection '{collection_name}'...")
    batch_size = 1000 # Qdrant delete operations can handle batches
    deleted_count = 0
    for i in range(0, len(point_ids), batch_size):
        batch_ids = point_ids[i:i + batch_size]
        try:
            qdrant_client.delete(
                collection_name=collection_name,
                points_selector={"points": batch_ids}
            )
            deleted_count += len(batch_ids)
            logging.info(f"Deleted {deleted_count}/{len(point_ids)} entries from Qdrant.")
        except Exception as e:
            logging.error(f"Error deleting batch of Qdrant entries (batch {i//batch_size + 1}): {e}")
            # Continue to next batch even if one fails

    logging.info(f"Finished deleting Qdrant entries. Total deleted: {deleted_count}.")

def main():
    logging.info("Starting Qdrant deletion script...")

    qdrant_url = os.environ.get("QDRANT_URL")
    qdrant_api_key = os.environ.get("QDRANT_API_KEY")
    qdrant_collection_name = "IP_Assets"

    if not qdrant_url or not qdrant_api_key:
        logging.error("QDRANT_URL or QDRANT_API_KEY environment variables are not set. Exiting.")
        return

    qdrant_client = QdrantClient(url=qdrant_url, api_key=qdrant_api_key)

    # Step 1: Get all patent reg_nos and point_ids from Qdrant, and total patent count
    qdrant_patent_data, total_qdrant_patent_count = get_qdrant_patent_reg_nos(qdrant_client, qdrant_collection_name)
    
    if total_qdrant_patent_count == 0:
        logging.info("No patent entries (ip_type=Patent) found in Qdrant. Exiting.")
        return
    
    logging.info(f"Proceeding with {len(qdrant_patent_data)} patent entries that have 'reg_no' out of {total_qdrant_patent_count} total patents in Qdrant.")

    # Step 2: Get current TRO CPC/IPC classifications (new format)
    current_tro_classifications = get_tro_cpc_ipc_classifications()
    if not current_tro_classifications:
        logging.error("Failed to retrieve current TRO CPC/IPC classifications. Exiting.")
        return
    logging.info(f"Loaded {len(current_tro_classifications)} current TRO CPC/IPC classifications.")

    # Step 3: Get classifications from PostgreSQL for the patents found in Qdrant
    db_patent_classifications = get_db_patent_classifications(set(qdrant_patent_data.keys()))
    if not db_patent_classifications:
        logging.info("No patent classifications retrieved from the database. Exiting.")
        return

    # Step 4: Identify point_ids to delete
    point_ids_to_delete = identify_patents_for_deletion(
        qdrant_patent_data,
        db_patent_classifications,
        current_tro_classifications
    )

    # Step 5: Delete entries from Qdrant
    delete_qdrant_entries(qdrant_client, qdrant_collection_name, point_ids_to_delete)

    logging.info("Qdrant deletion script finished.")

if __name__ == '__main__':
    main()